from django.contrib import admin
from django.utils.html import format_html

from .models import *


@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'code', 'email')
    readonly_fields = ('id', 'created_at', 'updated_at')
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'description', 'is_active')
        }),
        ('Contact Information', {
            'fields': ('address', 'phone_number', 'email', 'website')
        }),
        ('System Information', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'category', 'is_active', 'requires_verification', 'can_have_multiple')
    list_filter = ('category', 'is_active', 'requires_verification', 'can_have_multiple')
    search_fields = ('name', 'code', 'description')
    readonly_fields = ('created_at', 'updated_at')
    filter_horizontal = ('permissions',)
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'description', 'category')
        }),
        ('Settings', {
            'fields': ('is_active', 'requires_verification', 'can_have_multiple')
        }),
        ('Permissions', {
            'fields': ('permissions',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(UserOrganizationRole)
class UserOrganizationRoleAdmin(admin.ModelAdmin):
    list_display = ('user', 'organization', 'role', 'is_active', 'is_verified', 'assigned_at')
    list_filter = ('role', 'organization', 'is_active', 'is_verified', 'assigned_at')
    search_fields = ('user__username', 'user__email', 'organization__name', 'role__name')
    readonly_fields = ('assigned_at', 'verified_at')
    autocomplete_fields = ('user', 'organization', 'role', 'assigned_by', 'verified_by')

    fieldsets = (
        ('Assignment', {
            'fields': ('user', 'organization', 'role', 'is_active')
        }),
        ('Verification', {
            'fields': ('is_verified', 'verified_at', 'verified_by')
        }),
        ('Assignment Details', {
            'fields': ('assigned_at', 'assigned_by', 'additional_data')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'organization', 'role')


@admin.register(CustomUser)
class CustomUserAdmin(admin.ModelAdmin):
    list_display = ('username', 'email', 'get_roles', 'is_verified', 'is_active', 'date_joined')
    list_filter = ('is_verified', 'is_active', 'date_joined')
    search_fields = ('username', 'email', 'first_name', 'last_name')
    readonly_fields = ('date_joined', 'last_login', 'created_at', 'updated_at')

    fieldsets = (
        ('User Information', {
            'fields': ('username', 'email', 'first_name', 'last_name')
        }),
        ('Profile', {
            'fields': ('bio', 'profile_pic', 'phone_number', 'date_of_birth', 'address')
        }),
        ('Status', {
            'fields': ('is_active', 'is_verified', 'is_staff', 'is_superuser')
        }),
        ('System Information', {
            'fields': ('date_joined', 'last_login', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_roles(self, obj):
        roles = obj.user_organization_roles.select_related('role', 'organization').all()
        if not roles:
            return "No roles"

        role_list = []
        for role_assignment in roles:
            status = "✓" if role_assignment.is_verified else "⏳"
            role_list.append(f"{status} {role_assignment.role.name} ({role_assignment.organization.code})")

        return format_html("<br>".join(role_list))

    get_roles.short_description = "Roles"


# Legacy model admins - marked as deprecated
@admin.register(Teacher)
class TeacherAdmin(admin.ModelAdmin):
    list_display = ('user', 'organization', 'is_verified', 'phone_number')
    list_filter = ('is_verified', 'organization')
    search_fields = ('user__username', 'user__email', 'phone_number')
    readonly_fields = ('user',)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'organization')

    class Meta:
        verbose_name = "Teacher (Legacy - Use Role System Instead)"


@admin.register(Parent)
class ParentAdmin(admin.ModelAdmin):
    list_display = ('user', 'organization', 'is_verified', 'phone_number')
    list_filter = ('is_verified', 'organization')
    search_fields = ('user__username', 'user__email', 'phone_number')
    readonly_fields = ('user',)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'organization')

    class Meta:
        verbose_name = "Parent (Legacy - Use Role System Instead)"


@admin.register(Student)
class StudentAdmin(admin.ModelAdmin):
    list_display = ('user', 'organization', 'address', 'birthday')
    list_filter = ('organization', 'birthday')
    search_fields = ('user__username', 'user__email', 'address')
    readonly_fields = ('user',)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'organization')

    class Meta:
        verbose_name = "Student (Legacy - Use Role System Instead)"


@admin.register(Classes)
class ClassesAdmin(admin.ModelAdmin):
    list_display = ('name', 'organization', 'get_teacher', 'get_student_count', 'is_active')
    list_filter = ('organization', 'is_active', 'created_at')
    search_fields = ('name', 'organization__name', 'description')
    readonly_fields = ('created_at', 'updated_at')
    autocomplete_fields = ('organization', 'teacher', 'teacher_role')
    filter_horizontal = ('students', 'student_roles')

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'organization', 'description', 'is_active')
        }),
        ('Teacher Assignment', {
            'fields': ('teacher', 'teacher_role'),
            'description': 'Use either legacy teacher field or new role-based assignment'
        }),
        ('Student Enrollment', {
            'fields': ('students', 'student_roles'),
            'description': 'Use either legacy student field or new role-based enrollment'
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_teacher(self, obj):
        if obj.teacher_role:
            return f"{obj.teacher_role.user.username} (Role-based)"
        elif obj.teacher:
            return f"{obj.teacher.user.username} (Legacy)"
        return "No teacher assigned"
    get_teacher.short_description = "Teacher"

    def get_student_count(self, obj):
        legacy_count = obj.students.count()
        role_count = obj.student_roles.count()
        return f"{legacy_count + role_count} ({legacy_count} legacy + {role_count} role-based)"
    get_student_count.short_description = "Students"


@admin.register(Subject)
class SubjectAdmin(admin.ModelAdmin):
    list_display = ('name', 'classname', 'organization', 'get_teacher', 'is_active')
    list_filter = ('organization', 'is_active', 'created_at')
    search_fields = ('name', 'classname__name', 'organization__name', 'description')
    readonly_fields = ('organization', 'created_at', 'updated_at')
    autocomplete_fields = ('classname', 'teacher', 'teacher_role')

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'classname', 'description', 'is_active')
        }),
        ('Teacher Assignment', {
            'fields': ('teacher', 'teacher_role'),
            'description': 'Use either legacy teacher field or new role-based assignment'
        }),
        ('System Information', {
            'fields': ('organization', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_teacher(self, obj):
        if obj.teacher_role:
            return f"{obj.teacher_role.user.username} (Role-based)"
        elif obj.teacher:
            return f"{obj.teacher.user.username} (Legacy)"
        return "No teacher assigned"
    get_teacher.short_description = "Teacher"


@admin.register(Topic)
class TopicAdmin(admin.ModelAdmin):
    list_display = ('name', 'subject', 'organization', 'is_active')
    list_filter = ('organization', 'is_active', 'created_at')
    search_fields = ('name', 'subject__name', 'organization__name', 'description')
    readonly_fields = ('organization', 'created_at', 'updated_at')
    autocomplete_fields = ('subject',)


@admin.register(Assignment)
class AssignmentAdmin(admin.ModelAdmin):
    list_display = ('title', 'subject', 'organization', 'due_date', 'created_by', 'is_active')
    list_filter = ('organization', 'is_active', 'due_date', 'created_at')
    search_fields = ('title', 'subject__name', 'organization__name', 'description')
    readonly_fields = ('organization', 'created_at', 'updated_at')
    autocomplete_fields = ('subject', 'created_by')


@admin.register(Submission)
class SubmissionAdmin(admin.ModelAdmin):
    list_display = ('assignment', 'get_student', 'organization', 'submitted_at', 'grade')
    list_filter = ('organization', 'submitted_at', 'grade')
    search_fields = ('assignment__title', 'organization__name', 'content')
    readonly_fields = ('organization', 'submitted_at', 'graded_at')
    autocomplete_fields = ('assignment', 'student', 'student_role', 'graded_by')

    def get_student(self, obj):
        if obj.student_role:
            return f"{obj.student_role.user.username} (Role-based)"
        elif obj.student:
            return f"{obj.student.user.username} (Legacy)"
        return "Unknown student"
    get_student.short_description = "Student"


@admin.register(Examination)
class ExaminationAdmin(admin.ModelAdmin):
    list_display = ('title', 'subject', 'organization', 'start_time', 'end_time', 'created_by', 'is_active')
    list_filter = ('organization', 'is_active', 'start_time', 'created_at')
    search_fields = ('title', 'subject__name', 'organization__name', 'description')
    readonly_fields = ('organization', 'created_at', 'updated_at')
    autocomplete_fields = ('subject', 'created_by')


@admin.register(ExamSubmission)
class ExamSubmissionAdmin(admin.ModelAdmin):
    list_display = ('examination', 'get_student', 'organization', 'submitted_at', 'score')
    list_filter = ('organization', 'submitted_at', 'score')
    search_fields = ('examination__title', 'organization__name')
    readonly_fields = ('organization', 'submitted_at', 'graded_at')
    autocomplete_fields = ('examination', 'student', 'student_role', 'graded_by')

    def get_student(self, obj):
        if obj.student_role:
            return f"{obj.student_role.user.username} (Role-based)"
        elif obj.student:
            return f"{obj.student.user.username} (Legacy)"
        return "Unknown student"
    get_student.short_description = "Student"
