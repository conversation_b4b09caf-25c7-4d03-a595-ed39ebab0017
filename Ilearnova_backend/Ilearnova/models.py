import uuid

from django.contrib.auth.models import AbstractUser, Permission
from django.db import models


class Organization(models.Model):
    """
    Represents a school, educational institution, or organization.
    This enables multi-tenancy and data isolation.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=20, unique=True, help_text="Unique organization code for registration")
    description = models.TextField(blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    website = models.URLField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Organization"
        verbose_name_plural = "Organizations"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"


class UserRole(models.Model):
    """
    Defines available user roles/types in the system.
    This allows dynamic addition of new roles without code changes.
    """
    ROLE_CATEGORIES = [
        ('admin', 'Administrative'),
        ('academic', 'Academic'),
        ('student', 'Student'),
        ('parent', 'Parent/Guardian'),
        ('support', 'Support Staff'),
        ('external', 'External'),
    ]

    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=50, unique=True, help_text="Unique code for programmatic access")
    description = models.TextField(blank=True, null=True)
    category = models.CharField(max_length=20, choices=ROLE_CATEGORIES, default='academic')
    is_active = models.BooleanField(default=True)
    requires_verification = models.BooleanField(default=False, help_text="Whether users with this role need verification")
    can_have_multiple = models.BooleanField(default=True, help_text="Whether a user can have multiple instances of this role")
    permissions = models.ManyToManyField(Permission, blank=True, help_text="Default permissions for this role")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "User Role"
        verbose_name_plural = "User Roles"
        ordering = ['category', 'name']

    def __str__(self):
        return self.name


class CustomUser(AbstractUser):
    """
    Extended user model with organization support and flexible role system.
    Removed static boolean fields in favor of dynamic role assignments.
    """
    email = models.EmailField(unique=True)
    bio = models.TextField(blank=True, null=True)
    profile_pic = models.ImageField(upload_to='profiles/', blank=True, null=True)
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    date_of_birth = models.DateField(blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    is_verified = models.BooleanField(default=False, help_text="Global verification status")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Backward compatibility properties
    @property
    def is_teacher(self):
        """Backward compatibility: Check if user has teacher role in any organization"""
        return self.user_organization_roles.filter(role__code='teacher').exists()

    @property
    def is_student(self):
        """Backward compatibility: Check if user has student role in any organization"""
        return self.user_organization_roles.filter(role__code='student').exists()

    @property
    def is_parent(self):
        """Backward compatibility: Check if user has parent role in any organization"""
        return self.user_organization_roles.filter(role__code='parent').exists()

    def __str__(self):
        return self.username

    def get_roles_in_organization(self, organization):
        """Get all roles for this user in a specific organization"""
        return self.user_organization_roles.filter(organization=organization)

    def has_role_in_organization(self, role_code, organization):
        """Check if user has a specific role in an organization"""
        return self.user_organization_roles.filter(
            role__code=role_code,
            organization=organization,
            is_active=True
        ).exists()

    def get_organizations(self):
        """Get all organizations this user belongs to"""
        return Organization.objects.filter(
            id__in=self.user_organization_roles.values_list('organization_id', flat=True)
        ).distinct()


class UserOrganizationRole(models.Model):
    """
    Links users to organizations with specific roles.
    Enables organization-scoped roles and multi-role assignments.
    """
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='user_organization_roles')
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='organization_user_roles')
    role = models.ForeignKey(UserRole, on_delete=models.CASCADE, related_name='role_assignments')
    is_active = models.BooleanField(default=True)
    is_verified = models.BooleanField(default=False, help_text="Role-specific verification status")
    assigned_at = models.DateTimeField(auto_now_add=True)
    assigned_by = models.ForeignKey(
        CustomUser,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_roles'
    )
    verified_at = models.DateTimeField(null=True, blank=True)
    verified_by = models.ForeignKey(
        CustomUser,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='verified_roles'
    )
    additional_data = models.JSONField(
        default=dict,
        blank=True,
        help_text="Role-specific additional data (e.g., employee ID, student ID, etc.)"
    )

    class Meta:
        verbose_name = "User Organization Role"
        verbose_name_plural = "User Organization Roles"
        unique_together = ['user', 'organization', 'role']
        indexes = [
            models.Index(fields=['user', 'organization']),
            models.Index(fields=['organization', 'role']),
            models.Index(fields=['user', 'role']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.role.name} at {self.organization.name}"

    def save(self, *args, **kwargs):
        # Auto-verify if role doesn't require verification
        if not self.role.requires_verification and not self.is_verified:
            self.is_verified = True
            if not self.verified_at:
                from django.utils import timezone
                self.verified_at = timezone.now()
        super().save(*args, **kwargs)

# Legacy models - kept for backward compatibility but will be deprecated
# These will be replaced by the new role-based system
class Teacher(models.Model):
    """
    DEPRECATED: Use UserOrganizationRole with 'teacher' role instead.
    Kept for backward compatibility during migration.
    """
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE)
    id_document = models.FileField(upload_to='ids/')
    phone_number = models.CharField(max_length=15)
    address = models.CharField(max_length=255)
    is_verified = models.BooleanField(default=False)
    # Add organization for multi-tenancy
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Organization this teacher belongs to"
    )

    def __str__(self):
        return self.user.username

    class Meta:
        verbose_name = "Teacher (Legacy)"
        verbose_name_plural = "Teachers (Legacy)"


class Parent(models.Model):
    """
    DEPRECATED: Use UserOrganizationRole with 'parent' role instead.
    Kept for backward compatibility during migration.
    """
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE)
    phone_number = models.CharField(max_length=15)
    id_document = models.FileField(upload_to='ids/')
    is_verified = models.BooleanField(default=False)
    # Add organization for multi-tenancy
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Organization this parent belongs to"
    )

    def __str__(self):
        return self.user.username

    class Meta:
        verbose_name = "Parent (Legacy)"
        verbose_name_plural = "Parents (Legacy)"


class Student(models.Model):
    """
    DEPRECATED: Use UserOrganizationRole with 'student' role instead.
    Kept for backward compatibility during migration.
    """
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE)
    address = models.CharField(max_length=255)
    birthday = models.DateField(null=True, blank=True)
    # Add organization for multi-tenancy
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Organization this student belongs to"
    )

    def __str__(self):
        return self.user.username

    class Meta:
        verbose_name = "Student (Legacy)"
        verbose_name_plural = "Students (Legacy)"


# Updated models with organization support
class Classes(models.Model):
    """
    Represents a class/course within an organization.
    Updated to support organization-scoped data.
    """
    name = models.CharField(max_length=100)
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='classes'
    )
    teacher = models.ForeignKey(
        Teacher,
        on_delete=models.CASCADE,
        related_name='classes',
        null=True,
        blank=True
    )
    # New field for role-based teacher assignment
    teacher_role = models.ForeignKey(
        UserOrganizationRole,
        on_delete=models.CASCADE,
        related_name='taught_classes',
        null=True,
        blank=True,
        limit_choices_to={'role__code': 'teacher'},
        help_text="Teacher assigned via role system"
    )
    students = models.ManyToManyField(
        Student,
        related_name='classes',
        blank=True
    )
    # New field for role-based student enrollment
    student_roles = models.ManyToManyField(
        UserOrganizationRole,
        related_name='enrolled_classes',
        blank=True,
        limit_choices_to={'role__code': 'student'},
        help_text="Students enrolled via role system"
    )
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Class"
        verbose_name_plural = "Classes"
        unique_together = ['name', 'organization']
        ordering = ['organization', 'name']

    def __str__(self):
        return f"{self.name} ({self.organization.code})"


class Subject(models.Model):
    """
    Represents a subject within a class and organization.
    Updated to support organization-scoped data.
    """
    name = models.CharField(max_length=100)
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='subjects'
    )
    teacher = models.ForeignKey(
        Teacher,
        on_delete=models.CASCADE,
        related_name='subjects',
        null=True,
        blank=True
    )
    # New field for role-based teacher assignment
    teacher_role = models.ForeignKey(
        UserOrganizationRole,
        on_delete=models.CASCADE,
        related_name='taught_subjects',
        null=True,
        blank=True,
        limit_choices_to={'role__code': 'teacher'},
        help_text="Teacher assigned via role system"
    )
    classname = models.ForeignKey(
        Classes,
        on_delete=models.CASCADE,
        related_name='subjects'
    )
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Subject"
        verbose_name_plural = "Subjects"
        unique_together = ['name', 'classname']
        ordering = ['organization', 'classname', 'name']

    def __str__(self):
        return f"{self.name} - {self.classname.name} ({self.organization.code})"

    def save(self, *args, **kwargs):
        # Auto-set organization from class
        if self.classname and not self.organization:
            self.organization = self.classname.organization
        super().save(*args, **kwargs)


class Topic(models.Model):
    """
    Represents a topic within a subject and organization.
    Updated to support organization-scoped data.
    """
    name = models.CharField(max_length=100)
    subject = models.ForeignKey(
        Subject,
        on_delete=models.CASCADE,
        related_name='topics'
    )
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='topics',
        null=True,
        blank=True
    )
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Topic"
        verbose_name_plural = "Topics"
        unique_together = ['name', 'subject']
        ordering = ['subject', 'name']

    def __str__(self):
        return f"{self.name} ({self.subject.name})"

    def save(self, *args, **kwargs):
        # Auto-set organization from subject
        if self.subject and not self.organization:
            self.organization = self.subject.organization
        super().save(*args, **kwargs)


class Assignment(models.Model):
    """
    Represents an assignment within a subject and organization.
    Updated to support organization-scoped data.
    """
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    due_date = models.DateTimeField()
    subject = models.ForeignKey(
        Subject,
        on_delete=models.CASCADE,
        related_name='assignments'
    )
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='assignments',
        null=True,
        blank=True
    )
    created_by = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='created_assignments'
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Assignment"
        verbose_name_plural = "Assignments"
        ordering = ['organization', 'subject', 'due_date']

    def __str__(self):
        return f"{self.title} ({self.subject.name})"

    def save(self, *args, **kwargs):
        # Auto-set organization from subject
        if self.subject and not self.organization:
            self.organization = self.subject.organization
        super().save(*args, **kwargs)


class Submission(models.Model):
    """
    Represents a student's submission for an assignment.
    Updated to support organization-scoped data.
    """
    assignment = models.ForeignKey(
        Assignment,
        on_delete=models.CASCADE,
        related_name='submissions'
    )
    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='submissions',
        null=True,
        blank=True
    )
    # New field for role-based student assignment
    student_role = models.ForeignKey(
        UserOrganizationRole,
        on_delete=models.CASCADE,
        related_name='submissions',
        null=True,
        blank=True,
        limit_choices_to={'role__code': 'student'},
        help_text="Student who submitted via role system"
    )
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='submissions',
        null=True,
        blank=True
    )
    submitted_at = models.DateTimeField(auto_now_add=True)
    content = models.TextField()
    submitted_file = models.FileField(upload_to='submissions/')
    grade = models.FloatField(null=True, blank=True)
    graded_by = models.ForeignKey(
        CustomUser,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='graded_submissions'
    )
    graded_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "Submission"
        verbose_name_plural = "Submissions"
        ordering = ['assignment', 'submitted_at']

    def __str__(self):
        student_name = (
            self.student.user.username if self.student
            else self.student_role.user.username if self.student_role
            else "Unknown"
        )
        return f"{self.assignment.title} - {student_name}"

    def save(self, *args, **kwargs):
        # Auto-set organization from assignment
        if self.assignment and not self.organization:
            self.organization = self.assignment.organization
        super().save(*args, **kwargs)


class Examination(models.Model):
    """
    Represents an examination within a subject and organization.
    Updated to support organization-scoped data.
    """
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    subject = models.ForeignKey(
        Subject,
        on_delete=models.CASCADE,
        related_name='exams'
    )
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='examinations',
        null=True,
        blank=True
    )
    created_by = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='created_exams'
    )
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Examination"
        verbose_name_plural = "Examinations"
        ordering = ['organization', 'subject', 'start_time']

    def __str__(self):
        return f"{self.title} ({self.subject.name})"

    def save(self, *args, **kwargs):
        # Auto-set organization from subject
        if self.subject and not self.organization:
            self.organization = self.subject.organization
        super().save(*args, **kwargs)


class ExamSubmission(models.Model):
    """
    Represents a student's submission for an examination.
    Updated to support organization-scoped data.
    """
    examination = models.ForeignKey(
        Examination,
        on_delete=models.CASCADE,
        related_name='exam_submissions'
    )
    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='exam_submissions',
        null=True,
        blank=True
    )
    # New field for role-based student assignment
    student_role = models.ForeignKey(
        UserOrganizationRole,
        on_delete=models.CASCADE,
        related_name='exam_submissions',
        null=True,
        blank=True,
        limit_choices_to={'role__code': 'student'},
        help_text="Student who submitted via role system"
    )
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='exam_submissions',
        null=True,
        blank=True
    )
    submitted_at = models.DateTimeField(auto_now_add=True)
    answers = models.JSONField(default=dict)
    score = models.FloatField(null=True, blank=True)
    graded_by = models.ForeignKey(
        CustomUser,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='graded_exam_submissions'
    )
    graded_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "Exam Submission"
        verbose_name_plural = "Exam Submissions"
        ordering = ['examination', 'submitted_at']

    def __str__(self):
        student_name = (
            self.student.user.username if self.student
            else self.student_role.user.username if self.student_role
            else "Unknown"
        )
        return f"{self.examination.title} - {student_name}"

    def save(self, *args, **kwargs):
        # Auto-set organization from examination
        if self.examination and not self.organization:
            self.organization = self.examination.organization
        super().save(*args, **kwargs)
